{"name": "babel-cli", "version": "6.26.0", "description": "Babel command line.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-cli", "keywords": ["6to5", "babel", "es6", "transpile", "transpiler", "babel-cli", "compiler"], "dependencies": {"babel-core": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-register": "^6.26.0", "babel-runtime": "^6.26.0", "commander": "^2.11.0", "convert-source-map": "^1.5.0", "fs-readdir-recursive": "^1.0.0", "glob": "^7.1.2", "lodash": "^4.17.4", "output-file-sync": "^1.1.2", "path-is-absolute": "^1.0.1", "slash": "^1.0.0", "source-map": "^0.5.6", "v8flags": "^2.1.1"}, "optionalDependencies": {"chokidar": "^1.6.1"}, "devDependencies": {"babel-helper-fixtures": "^6.26.0"}, "bin": {"babel-doctor": "./bin/babel-doctor.js", "babel": "./bin/babel.js", "babel-node": "./bin/babel-node.js", "babel-external-helpers": "./bin/babel-external-helpers.js"}}
# babel-cli

> Babel command line.
 
In addition, various entry point scripts live in the top-level package at `babel-cli/bin`.

There are some shell-executable utility scripts, `babel-external-helpers.js` and `babel-node.js`, and the main Babel cli script, `babel.js`.

## Install

```sh
npm install --save-dev babel-cli
```

## Usage 

```sh
babel script.js
```

For more in depth documentation see: http://babeljs.io/docs/usage/cli/
